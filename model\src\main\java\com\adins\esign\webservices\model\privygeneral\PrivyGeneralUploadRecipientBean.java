package com.adins.esign.webservices.model.privygeneral;

import java.util.List;
import com.google.gson.annotations.SerializedName;

public class PrivyGeneralUploadRecipientBean {
	@SerializedName("user_type") private String userType;
	@SerializedName("id_user") private String idUser;
	@SerializedName("signer_type") private String signerType;
	private String enterpriseToken;
	@SerializedName("notify_user") private String notifyUser;
	@SerializedName("drag_n_drop") private boolean dragNDrop;
	private String detail;
	private String posX;
	private String posY;
	private String signPage;
	@SerializedName("user_data") private PrivyGeneralUploadDocumentUserDataBean userData;
	private int autosign;
	@SerializedName("sign_positions") private List<PrivyGeneralSignPositionBean> signPositions;
	
	public String getUserType() {
		return userType;
	}
	public void setUserType(String userType) {
		this.userType = userType;
	}
	public String getIdUser() {
		return idUser;
	}
	public void setIdUser(String idUser) {
		this.idUser = idUser;
	}
	public String getSignerType() {
		return signerType;
	}
	public void setSignerType(String signerType) {
		this.signerType = signerType;
	}
	public String getEnterpriseToken() {
		return enterpriseToken;
	}
	public void setEnterpriseToken(String enterpriseToken) {
		this.enterpriseToken = enterpriseToken;
	}
	public String getNotifyUser() {
		return notifyUser;
	}
	public void setNotifyUser(String notifyUser) {
		this.notifyUser = notifyUser;
	}
	public boolean isDragNDrop() {
		return dragNDrop;
	}
	public void setDragNDrop(boolean dragNDrop) {
		this.dragNDrop = dragNDrop;
	}
	public String getDetail() {
		return detail;
	}
	public void setDetail(String detail) {
		this.detail = detail;
	}
	public String getPosX() {
		return posX;
	}
	public void setPosX(String posX) {
		this.posX = posX;
	}
	public String getPosY() {
		return posY;
	}
	public void setPosY(String posY) {
		this.posY = posY;
	}
	public String getSignPage() {
		return signPage;
	}
	public void setSignPage(String signPage) {
		this.signPage = signPage;
	}
	public PrivyGeneralUploadDocumentUserDataBean getUserData() {
		return userData;
	}
	public void setUserData(PrivyGeneralUploadDocumentUserDataBean userData) {
		this.userData = userData;
	}
	public int getAutosign() {
		return autosign;
	}
	public void setAutosign(int autosign) {
		this.autosign = autosign;
	}
	public List<PrivyGeneralSignPositionBean> getSignPositions() {
		return signPositions;
	}
	public void setSignPositions(List<PrivyGeneralSignPositionBean> signPositions) {
		this.signPositions = signPositions;
	}
}
