package com.adins.esign.webservices.model.privygeneral;

import com.google.gson.annotations.SerializedName;

/**
 * Bean class representing individual sign position for Privy multiple sign location support
 */
public class PrivyGeneralSignPositionBean {
    @SerializedName("posX") private String posX;
    @SerializedName("posY") private String posY;
    @SerializedName("signPage") private String signPage;
    
    public PrivyGeneralSignPositionBean() {
    }
    
    public PrivyGeneralSignPositionBean(String posX, String posY, String signPage) {
        this.posX = posX;
        this.posY = posY;
        this.signPage = signPage;
    }
    
    public String getPosX() {
        return posX;
    }
    
    public void setPosX(String posX) {
        this.posX = posX;
    }
    
    public String getPosY() {
        return posY;
    }
    
    public void setPosY(String posY) {
        this.posY = posY;
    }
    
    public String getSignPage() {
        return signPage;
    }
    
    public void setSignPage(String signPage) {
        this.signPage = signPage;
    }
}
