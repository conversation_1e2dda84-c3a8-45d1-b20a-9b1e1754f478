package com.adins.esign.businesslogic.impl.interfacing;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.adins.am.businesslogic.BaseLogic;
import com.adins.esign.businesslogic.api.CloudStorageLogic;
import com.adins.esign.businesslogic.api.PersonalDataEncryptionLogic;
import com.adins.esign.businesslogic.api.SigningProcessAuditTrailLogic;
import com.adins.esign.businesslogic.api.interfacing.PrivyGeneralLogic;
import com.adins.esign.constants.GlobalVal;
import com.adins.esign.constants.HttpHeaders;
import com.adins.esign.model.MsDocTemplate;
import com.adins.esign.model.MsDocTemplateSignLoc;
import com.adins.esign.model.MsLov;
import com.adins.esign.model.MsTenant;
import com.adins.esign.model.MsVendor;
import com.adins.esign.model.MsVendorRegisteredUser;
import com.adins.esign.model.MsVendoroftenant;
import com.adins.esign.model.TrBalanceMutation;
import com.adins.esign.model.TrDocumentD;
import com.adins.esign.model.TrPsreSigningConfirmation;
import com.adins.esign.model.TrSigningProcessAuditTrail;
import com.adins.esign.model.TrSigningProcessAuditTrailDetail;
import com.adins.esign.model.custom.ManualSignerBean;
import com.adins.esign.model.custom.RegisterVerificationStatusBean;
import com.adins.esign.model.custom.SendDocFullApiRequestBean;
import com.adins.esign.model.custom.SendDocFullApiSignerBean;
import com.adins.esign.model.custom.SignatureDetailBean;
import com.adins.esign.model.custom.SignerBean;
import com.adins.esign.model.custom.SigningProcessAuditTrailBean;
import com.adins.esign.model.custom.UserBean;
import com.adins.esign.model.custom.privygeneral.PrivyGeneralErrorBean;
import com.adins.esign.model.custom.privygeneral.PrivyGeneralRegisterResponseContainer;
import com.adins.esign.util.MssTool;
import com.adins.esign.util.PrivyUtils;
import com.adins.esign.webservices.model.DocumentConfinsRequestBean;
import com.adins.esign.webservices.model.InsertDocumentManualSignRequest;
import com.adins.esign.webservices.model.external.RegisterExternalRequest;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralGetTokenRequest;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralGetTokenResponse;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralOtpRequestSigningRequest;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralOtpRequestSigningResponse;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralOtpValidationRequest;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralOtpValidationResponse;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralRegisterRequest;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralRegisterResponse;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralRegisterStatusRequest;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralRegisterStatusResponse;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralUploadDocumentDocOwnerBean;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralUploadDocumentDocumentBean;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralUploadDocumentRequest;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralUploadDocumentResponse;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralUploadDocumentUserDataBean;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralUploadRecipientBean;
import com.adins.esign.webservices.model.privygeneral.PrivyGeneralSignPositionBean;
import com.adins.exceptions.PrivyException;
import com.adins.exceptions.StatusCode;
import com.adins.framework.persistence.dao.model.AuditContext;
import com.google.gson.Gson;

import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

@Component
public class GenericPrivyGeneralLogic extends BaseLogic implements PrivyGeneralLogic {
	
	private static final Logger LOG = LoggerFactory.getLogger(GenericPrivyGeneralLogic.class);
	
	@Value("${privy.general.base.uri}") private String baseUrl;
	@Value("${privy.general.token.uri}") private String tokenUrl;
	@Value("${privy.general.register.uri}") private String registerUrl;
	@Value("${privy.general.registerstatus.uri}") private String registerStatusUrl;
	@Value("${privy.general.uploaddoc.uri}") private String uploadDocUrl;
	@Value("${privy.general.otprequest.uri}") private String otpRequestUrl;
	@Value("${privy.general.otpvalidation.uri}") private String otpValidationUrl;
	
	@Autowired private Gson gson;
	@Autowired private PersonalDataEncryptionLogic personalDataEncLogic;
	@Autowired private SigningProcessAuditTrailLogic auditTrailLogic;

	private static final String REF_NUMBER_PREFIX = "ADINS";
	private static final String CONST_MESSAGES = "messages"; 
	private static final String CONST_REFNUMBER_REPLACE = "[^a-zA-Z0-9]";
	private static final String CONST_DOCUMENT_FILE_PDFBASE64 = "data:application/pdf;base64,";
	private static final String CONST_SIGNERTYPE_SIGNER = "Signer";
	
	private Headers buildPrivyApiHeader(String jsonRequest, MsVendoroftenant vendoroftenant, AuditContext audit) {
		Date timestamp = new Date();
		String token = getToken(vendoroftenant, audit);
		
		Map<String, String> mapHeader = new HashMap<>();
		mapHeader.put(HttpHeaders.CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
		mapHeader.put(HttpHeaders.AUTHORIZATION, HttpHeaders.buildBearerToken(token));
		mapHeader.put("Timestamp", MssTool.formatDateToStringIn(timestamp, "yyyy-MM-dd'T'HH:mm:ssXXX"));
		mapHeader.put("Signature", generateSignature(jsonRequest, timestamp, vendoroftenant, audit));
		LOG.info("Privy header: {}", mapHeader);
		return Headers.of(mapHeader);
	}
	
	private String generateSignature(String jsonRequest, Date timestamp, MsVendoroftenant vendoroftenant, AuditContext audit) {
		String username = vendoroftenant.getClientId();
		String password = vendoroftenant.getClientSecret();
		
		try {
			return PrivyUtils.createSignature(timestamp, username, password, jsonRequest);
		} catch (Exception e) {
			throw new PrivyException(getMessage("businesslogic.privy.general.failedtogeneratesignature", null, audit));
		}
		
	}
	
	private String getToken(MsVendoroftenant vendoroftenant, AuditContext audit) {
		try {
			PrivyGeneralGetTokenResponse response = callGetTokenApi(vendoroftenant.getClientId(), vendoroftenant.getClientSecret());
			return response.getData().getAccessToken();
		} catch (Exception e) {
			throw new PrivyException(getMessage("businesslogic.privy.general.failedtogettoken", null, audit), e);
		}
	}
	
	private PrivyGeneralGetTokenResponse callGetTokenApi(String username, String password) throws IOException {
		
		// Header
		Map<String, String> headerMap = new HashMap<>();
		headerMap.put(HttpHeaders.CONTENT_TYPE, HttpHeaders.APPLICATION_JSON);
		headerMap.put(HttpHeaders.AUTHORIZATION, HttpHeaders.buildBasicAuthorization(username, password));
		Headers headers = Headers.of(headerMap);
		
		// Body
		PrivyGeneralGetTokenRequest request = new PrivyGeneralGetTokenRequest();
		request.setClientId(username);
		request.setClientSecret(password);
		request.setGrantType("client_credentials");
		String jsonRequest = gson.toJson(request);
		RequestBody body = RequestBody.create(MediaType.parse(HttpHeaders.APPLICATION_JSON), jsonRequest);
		
		// Prepare request
		Request okHttpRequest = new Request.Builder()
				.headers(headers)
				.url(baseUrl + tokenUrl)
				.post(body)
				.build();
		
		OkHttpClient client = new OkHttpClient.Builder()
				.connectTimeout(5L, TimeUnit.SECONDS)
				.readTimeout(10L, TimeUnit.SECONDS)
				.build();
		
		Date startTime = new Date();
		Response okHttpResponse = client.newCall(okHttpRequest).execute();
		Date finishTime = new Date();
		logProcessDuration("Get General Privy token", startTime, finishTime);
		
		String jsonResponse = okHttpResponse.body().string();
		
		LOG.info("Get General Privy token response: {}", jsonResponse);
		return gson.fromJson(jsonResponse, PrivyGeneralGetTokenResponse.class);
		
	}

	@Override
	public PrivyGeneralRegisterResponseContainer registerExternal(RegisterExternalRequest request, String trxNo, MsTenant tenant, MsVendor vendor, AuditContext audit) {
		
		try {
			
			MsVendoroftenant vendoroftenant = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
			
			// Prepare json request
			PrivyGeneralRegisterRequest registerRequest = new PrivyGeneralRegisterRequest();
			registerRequest.setReferenceNumber(REF_NUMBER_PREFIX + trxNo);
			registerRequest.setChannelId(vendoroftenant.getVendorChannelId());
			registerRequest.setInfo("");
			registerRequest.setEmail(request.getEmail());
			registerRequest.setPhone(MssTool.changePrefixToPlus62(request.getTlp()));
			registerRequest.setNik(request.getIdKtp());
			registerRequest.setName(request.getNama());
			registerRequest.setDob(request.getTglLahir());
			registerRequest.setSelfie(request.getSelfPhoto());
			registerRequest.setIdentity(request.getIdPhoto());
			String jsonRequest = gson.toJson(registerRequest);
			LOG.info("Register General Privy request: {}", jsonRequest);
			
			// Headers
			Headers headers = buildPrivyApiHeader(jsonRequest, vendoroftenant, audit);
			
			// Body
			RequestBody body = RequestBody.create(MediaType.parse(HttpHeaders.APPLICATION_JSON), jsonRequest);
			
			Request okHttpRequest = new Request.Builder()
					.headers(headers)
					.url(baseUrl + registerUrl)
					.post(body)
					.build();
			
			OkHttpClient client = new OkHttpClient.Builder()
					.connectTimeout(10L, TimeUnit.SECONDS)
					.readTimeout(60L, TimeUnit.SECONDS)
					.build();
			
			Date startTime = new Date();
			Response okHttpResponse = client.newCall(okHttpRequest).execute();
			Date finishTime = new Date();
			logProcessDuration("Register General Privy", startTime, finishTime);
			
			String jsonResponse = okHttpResponse.body().string();
			LOG.info("Register General Privy response code: {}, body: {}", okHttpResponse.code(), jsonResponse);
			
			PrivyGeneralRegisterResponseContainer responseContainer = new PrivyGeneralRegisterResponseContainer();
			responseContainer.setRequestBody(jsonRequest);
			responseContainer.setResponseBody(jsonResponse);
			responseContainer.setRegisterResponse(gson.fromJson(jsonResponse, PrivyGeneralRegisterResponse.class));
			return responseContainer;
			
		} catch (Exception e) {
			
			LOG.error("Register General Privy exception: {}", e.getLocalizedMessage(), e);
			PrivyGeneralRegisterResponse response = new PrivyGeneralRegisterResponse();
			response.setMessage(e.getLocalizedMessage());
			
			PrivyGeneralRegisterResponseContainer responseContainer = new PrivyGeneralRegisterResponseContainer();
			responseContainer.setRegisterResponse(response);
			return responseContainer;
			
		}
		
	}

	@SuppressWarnings("unchecked")
	@Override
	public String buildRegisterErrorMessage(PrivyGeneralRegisterResponse response, AuditContext audit) {
		
		if (StringUtils.isNotBlank(response.getMessage())) {
			return response.getMessage();
		}
		
		if (null == response.getError() || CollectionUtils.isEmpty(response.getError().getErrors())) {
			return StringUtils.EMPTY;
		}
		
		if (response.getError().getErrors().get(0) instanceof String) {
			return (String) response.getError().getErrors().get(0);
		}
		
		String messageKey = CONST_MESSAGES;
		
		// If not instance of Map
		if (!(response.getError().getErrors().get(0) instanceof Map<?, ?>)) {
			return "Registrasi gagal";
		}
		
		// If instance of Map
		Map<?, ?> errors = (Map<?, ?>) response.getError().getErrors().get(0);
		
		if (errors.get(messageKey) instanceof String) {
			return (String) errors.get(CONST_MESSAGES);
		}
		
		if (errors.get(messageKey) instanceof List<?>) {
			List<Object> errorList = (List<Object>) errors.get(messageKey);
			return (String) errorList.get(0);
		}
		
		return "Registrasi gagal";
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public String buildUploadDocErrorMessage(PrivyGeneralUploadDocumentResponse response, AuditContext audit) {
		
		String fail = "Fail Upload Doc Privy : ";
		
		if (StringUtils.isNotBlank(response.getMessage())) {
			return fail + response.getMessage();
		}
		
		if (null != response.getBlockReason()) {
			return fail + response.getBlockReason().getMessage();
		}
		
		if (null == response.getError() || CollectionUtils.isEmpty(response.getError().getErrors())) {
			return StringUtils.EMPTY;
		}
		
		if (response.getError().getErrors().get(0) instanceof String) {
			return (String) response.getError().getErrors().get(0);
		}
		
		String messageKey = CONST_MESSAGES;
		
		// If not instance of Map
		if (!(response.getError().getErrors().get(0) instanceof Map<?, ?>)) {
			return "Gagal kirim dokumen";
		}
		
		// If instance of Map
		Map<?, ?> errors = (Map<?, ?>) response.getError().getErrors().get(0);
		
		if (errors.get(messageKey) instanceof String) {
			return fail + (String) errors.get(CONST_MESSAGES);
		}
		
		if (errors.get(messageKey) instanceof List<?>) {
			List<Object> errorList = (List<Object>) errors.get(messageKey);
			return fail + (String) errorList.get(0);
		}
		
		return "Gagal kirim dokumen";
	}

	@Override
	public PrivyGeneralRegisterResponseContainer registerWithInvitationRequest(UserBean userData, String trxNo, MsTenant tenant, MsVendor vendor, AuditContext audit) {
		
		try {
			
			MsVendoroftenant vendoroftenant = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
			
			// Prepare json request
			PrivyGeneralRegisterRequest registerRequest = new PrivyGeneralRegisterRequest();
			registerRequest.setReferenceNumber(REF_NUMBER_PREFIX + trxNo);
			registerRequest.setChannelId(vendoroftenant.getVendorChannelId());
			registerRequest.setInfo("");
			registerRequest.setEmail(userData.getEmail());
			registerRequest.setPhone(MssTool.changePrefixToPlus62(userData.getUserPhone()));
			registerRequest.setNik(userData.getIdNo());
			registerRequest.setName(userData.getUserName());
			registerRequest.setDob(userData.getUserDob());
			registerRequest.setSelfie(userData.getSelfPhoto());
			registerRequest.setIdentity(userData.getIdPhoto());
			String jsonRequest = gson.toJson(registerRequest);
			LOG.info("Register General Privy request: {}", jsonRequest);
			
			// Headers
			Headers headers = buildPrivyApiHeader(jsonRequest, vendoroftenant, audit);
			
			// Body
			RequestBody body = RequestBody.create(MediaType.parse(HttpHeaders.APPLICATION_JSON), jsonRequest);
			
			Request okHttpRequest = new Request.Builder()
					.headers(headers)
					.url(baseUrl + registerUrl)
					.post(body)
					.build();
			
			OkHttpClient client = new OkHttpClient.Builder()
					.connectTimeout(10L, TimeUnit.SECONDS)
					.readTimeout(60L, TimeUnit.SECONDS)
					.build();
			
			Date startTime = new Date();
			Response okHttpResponse = client.newCall(okHttpRequest).execute();
			Date finishTime = new Date();
			logProcessDuration("Register General Privy", startTime, finishTime);
			
			String jsonResponse = okHttpResponse.body().string();
			LOG.info("Register General Privy response code: {}, body: {}", okHttpResponse.code(), jsonResponse);
			
			PrivyGeneralRegisterResponseContainer responseContainer = new PrivyGeneralRegisterResponseContainer();
			responseContainer.setRequestBody(jsonRequest);
			responseContainer.setResponseBody(jsonResponse);
			responseContainer.setRegisterResponse(gson.fromJson(jsonResponse, PrivyGeneralRegisterResponse.class));
			return responseContainer;
			
		} catch (Exception e) {
			
			LOG.error("Register General Privy exception: {}", e.getLocalizedMessage(), e);
			PrivyGeneralRegisterResponse response = new PrivyGeneralRegisterResponse();
			response.setMessage(e.getLocalizedMessage());
			
			PrivyGeneralRegisterResponseContainer responseContainer = new PrivyGeneralRegisterResponseContainer();
			responseContainer.setRegisterResponse(response);
			return responseContainer;
			
		}
	}

	@Override
	public PrivyGeneralRegisterStatusResponse checkRegisterStatus(MsVendoroftenant vendoroftenant, TrBalanceMutation mutation, AuditContext audit) throws IOException {
		
		PrivyGeneralRegisterStatusRequest request = new PrivyGeneralRegisterStatusRequest();
		request.setReferenceNumber(REF_NUMBER_PREFIX + mutation.getTrxNo());
		request.setChannelId(vendoroftenant.getVendorChannelId());
		request.setRegisterToken(mutation.getVendorTrxNo());
		request.setInfo(StringUtils.EMPTY);
		String jsonRequest = gson.toJson(request);
		LOG.info("Check register status General Privy request: {}", jsonRequest);
		
		// Headers
		Headers headers = buildPrivyApiHeader(jsonRequest, vendoroftenant, audit);
					
		// Body
		RequestBody body = RequestBody.create(MediaType.parse(HttpHeaders.APPLICATION_JSON), jsonRequest);
					
		Request okHttpRequest = new Request.Builder()
				.headers(headers)
				.url(baseUrl + registerStatusUrl)
				.post(body)
				.build();
		
		OkHttpClient client = new OkHttpClient.Builder()
				.connectTimeout(10L, TimeUnit.SECONDS)
				.readTimeout(60L, TimeUnit.SECONDS)
				.build();
		
		Date startTime = new Date();
		Response okHttpResponse = client.newCall(okHttpRequest).execute();
		Date finishTime = new Date();
		logProcessDuration("Check register status General Privy", startTime, finishTime);
					
		String jsonResponse = okHttpResponse.body().string();
		LOG.info("Check register status General Privy response code: {}, body: {}", okHttpResponse.code(), jsonResponse);
		return gson.fromJson(jsonResponse, PrivyGeneralRegisterStatusResponse.class);
	}

	@Override
	public String getRegisterStatusMessage(PrivyGeneralRegisterStatusResponse response, AuditContext audit) {
		if (null == response || null == response.getData()) {
			return StringUtils.EMPTY;
		}

		if ("waiting_verification".equalsIgnoreCase(response.getData().getStatus())) {
			return response.getData().getStatus();
		}
		
		if ("verified".equalsIgnoreCase(response.getData().getStatus()) || "registered".equalsIgnoreCase(response.getData().getStatus())) {
			return "Registrasi berhasil";
		}

		String rejectCode = response.getData().getRejectReason().getCode();
		if ("RC04".equals(rejectCode) || "RC06".equals(rejectCode)) {
			return "Verifikasi user gagal. Foto Diri tidak sesuai.";
		}

		if ("RC12".equals(rejectCode) || "RC13".equals(rejectCode)) {
			return "Verifikasi user gagal. Nama Lengkap tidak sesuai.";
		}

		LOG.info("Unhandled reject code: {}", rejectCode);
		return "Verifikasi gagal. " + response.getData().getRejectReason().getReason();
	}

	@Override
	public RegisterVerificationStatusBean getCheckRegisterVerificationResults(PrivyGeneralRegisterStatusResponse response, AuditContext audit) {
		if (null == response || null == response.getData()) {
			return new RegisterVerificationStatusBean();
		}

		if ("waiting_verification".equalsIgnoreCase(response.getData().getStatus())) {
			return new RegisterVerificationStatusBean();
		}
		
		if ("verified".equalsIgnoreCase(response.getData().getStatus()) || "registered".equalsIgnoreCase(response.getData().getStatus())) {
			RegisterVerificationStatusBean bean = new RegisterVerificationStatusBean();
			bean.setBirthDate(GlobalVal.CONST_TRUE);
			bean.setLiveness(GlobalVal.CONST_TRUE);
			bean.setName(GlobalVal.CONST_TRUE);
			bean.setNik(GlobalVal.NIK_REGISTERED);
			bean.setSelfieCheck(GlobalVal.CONST_TRUE);
			return bean;
		}

		String rejectCode = response.getData().getRejectReason().getCode();
		if ("RC04".equals(rejectCode) || "RC06".equals(rejectCode)) {
			RegisterVerificationStatusBean bean = new RegisterVerificationStatusBean();
			bean.setSelfieCheck(GlobalVal.CONST_FALSE);
			bean.setNik(GlobalVal.NIK_REGISTERED);
			return bean;
		}

		if ("RC12".equals(rejectCode) || "RC13".equals(rejectCode)) {
			RegisterVerificationStatusBean bean = new RegisterVerificationStatusBean();
			bean.setName(GlobalVal.CONST_FALSE);
			bean.setNik(GlobalVal.NIK_REGISTERED);
			return bean;
		}

		LOG.info("Unhandled reject code: {}", rejectCode);
		return new RegisterVerificationStatusBean();
	}

	@Override
	public PrivyGeneralUploadDocumentResponse uploadDoc(DocumentConfinsRequestBean normal,
			SendDocFullApiRequestBean external, InsertDocumentManualSignRequest manual, TrDocumentD docD, MsTenant tenant, MsVendor vendor, AuditContext audit) throws IOException {
		MsVendoroftenant vendoroftenant = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
		PrivyGeneralUploadDocumentRequest request = new PrivyGeneralUploadDocumentRequest();
		request.setChannelId(vendoroftenant.getVendorChannelId());
		request.setDocProcess(0);
		request.setCustomSignaturePlacement(true);
		request.setVisibility(true);
		
		PrivyGeneralUploadDocumentDocOwnerBean docOwner = new PrivyGeneralUploadDocumentDocOwnerBean();
		docOwner.setEnterpriseToken(vendoroftenant.getVendorEnterpriseToken());
		docOwner.setPrivyId(vendoroftenant.getVendorEnterpriseId());
		request.setDocOwner(docOwner);
		
		if (null != normal) {
			prepareUploadDoc(request, docD, tenant, normal);
		} else if (null != external) {
			prepareUploadDocExternal(request, docD, tenant, external);
		} else {
			prepareUploadDocManualSign(request, docD, manual);
		}
		
		String jsonRequest = gson.toJson(request);
		
		request.getDocument().setDocumentFile("{{base64File}}");
		String requestLog = gson.toJson(request);
		LOG.info("Upload Document General Privy request: {}", requestLog);
		
		// Headers
		Headers headers = buildPrivyApiHeader(jsonRequest, vendoroftenant, audit);
		
		// Body
		RequestBody body = RequestBody.create(MediaType.parse(HttpHeaders.APPLICATION_JSON), jsonRequest);
		
		Request okHttpRequest = new Request.Builder()
				.headers(headers)
				.url(baseUrl + uploadDocUrl)
				.post(body)
				.build();
		
		OkHttpClient client = new OkHttpClient.Builder()
				.connectTimeout(10L, TimeUnit.SECONDS)
				.readTimeout(60L, TimeUnit.SECONDS)
				.build();
		
		Date startTime = new Date();
		Response okHttpResponse = client.newCall(okHttpRequest).execute();
		Date finishTime = new Date();
		logProcessDuration("Upload Document General Privy", startTime, finishTime);
		
		String jsonResponse = okHttpResponse.body().string();
		LOG.info("Upload Document General Privy response code: {}, body: {}", okHttpResponse.code(), jsonResponse);
		return gson.fromJson(jsonResponse, PrivyGeneralUploadDocumentResponse.class);
	}
	
	private void prepareUploadDoc(PrivyGeneralUploadDocumentRequest request, TrDocumentD docD, MsTenant tenant, DocumentConfinsRequestBean normal) {
		request.setReferenceNumber(docD.getDocumentId().replaceAll(CONST_REFNUMBER_REPLACE, ""));
		
		MsDocTemplate docTemplate = docD.getMsDocTemplate();
		PrivyGeneralUploadDocumentDocumentBean document = new PrivyGeneralUploadDocumentDocumentBean();
		document.setBarcodePosition("0");
		document.setDocumentFile(CONST_DOCUMENT_FILE_PDFBASE64 + normal.getDocumentFile());
		document.setDocumentName(docTemplate.getDocTemplateName());
		document.setNotifyUser("0");
		document.setSignProcess("1".equals(normal.getIsSequence()) ? "0" : "1");
		
		request.setDocument(document);
		
		String enterpriseToken = request.getDocOwner().getEnterpriseToken();
		
		List<PrivyGeneralUploadRecipientBean> recepients = new ArrayList<>();
		for (SignerBean bean : normal.getSigner()) {
			List<MsDocTemplateSignLoc> signLocs = daoFactory.getDocumentDao().getListSignLocationByTemplateCodeTenantCodeAndLovSignerType
					(docTemplate.getDocTemplateCode(), bean.getSignerType(), tenant.getTenantCode());
			if (signLocs.isEmpty()) {
				continue;
			}
			
			PrivyGeneralUploadRecipientBean recepient = new PrivyGeneralUploadRecipientBean();
			recepient.setDetail("1");
			recepient.setNotifyUser("0");
			recepient.setDragNDrop(false);
			recepient.setSignerType(CONST_SIGNERTYPE_SIGNER);
			recepient.setAutosign(GlobalVal.CODE_DIGISIGN_AUTOSIGN.equals(bean.getSignAction()) ? 1 : 0);
			recepient.setEnterpriseToken(GlobalVal.CODE_DIGISIGN_AUTOSIGN.equals(bean.getSignAction()) ? enterpriseToken : "");
			
			if ("0".equals(bean.getIsActive())) {
				recepient.setIdUser(bean.getIdNo());
				recepient.setUserType("1");
				
				PrivyGeneralUploadDocumentUserDataBean userData = new PrivyGeneralUploadDocumentUserDataBean();
				userData.setDob(bean.getUserDob());
				userData.setEmail(bean.getEmail());
				userData.setName(bean.getUserName());
				userData.setPhone(bean.getUserPhone());
				
				recepient.setUserData(userData);
			} else {
				MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdNoAndVendorCode(bean.getIdNo(), GlobalVal.VENDOR_CODE_PRIVY_ID);
				recepient.setIdUser(vru.getVendorRegistrationId());
				recepient.setUserType("0");
			}
			
			MsDocTemplateSignLoc sl = signLocs.get(0);
			SignatureDetailBean psl = gson.fromJson(sl.getPrivySignLocation(), SignatureDetailBean.class);
			recepient.setPosX(String.valueOf(psl.getX()).replace(".0", ""));
			recepient.setPosY(String.valueOf(psl.getY()).replace(".0", ""));
			recepient.setSignPage(String.valueOf(sl.getSignPage()));
			
			recepients.add(recepient);
		}
		
		request.setRecipients(recepients);
 	}
	
	private void prepareUploadDocExternal(PrivyGeneralUploadDocumentRequest request, TrDocumentD docD, MsTenant tenant, SendDocFullApiRequestBean external) {
		request.setReferenceNumber(docD.getDocumentId().replaceAll(CONST_REFNUMBER_REPLACE, ""));
		
		PrivyGeneralUploadDocumentDocumentBean document = new PrivyGeneralUploadDocumentDocumentBean();
		MsDocTemplate dt = StringUtils.isBlank(external.getDocumentTemplateCode()) ? null : 
			daoFactory.getDocumentDao().getDocumentTemplateByCodeAndTenantCode(external.getDocumentTemplateCode(), tenant.getTenantCode());
		document.setBarcodePosition("0");
		document.setDocumentFile(CONST_DOCUMENT_FILE_PDFBASE64 + external.getDocumentFile());
		document.setDocumentName(dt != null ? dt.getDocTemplateName() : external.getDocumentName());
		document.setNotifyUser("0");
		document.setSignProcess("1".equals(external.getIsSequence()) ? "0" : "1");
		
		request.setDocument(document);
		
		String enterpriseToken = request.getDocOwner().getEnterpriseToken();
		
		List<PrivyGeneralUploadRecipientBean> recepients = new ArrayList<>();
		for (SendDocFullApiSignerBean bean : external.getSigners()) {
			PrivyGeneralUploadRecipientBean recepient = new PrivyGeneralUploadRecipientBean();
			recepient.setDetail("1");
			recepient.setNotifyUser("0");
			recepient.setDragNDrop(false);
			recepient.setSignerType(CONST_SIGNERTYPE_SIGNER);
			recepient.setAutosign(GlobalVal.CODE_DIGISIGN_AUTOSIGN.equals(bean.getSignAction()) ? 1 : 0);
			recepient.setEnterpriseToken(GlobalVal.CODE_DIGISIGN_AUTOSIGN.equals(bean.getSignAction()) ? enterpriseToken : "");
				
			MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByIdNoAndVendorCode(bean.getIdKtp(), GlobalVal.VENDOR_CODE_PRIVY_ID);
			recepient.setIdUser(vru.getVendorRegistrationId());
			recepient.setUserType("0");
			
			if (dt != null) {
				List<MsDocTemplateSignLoc> signLocs = daoFactory.getDocumentDao().getListSignLocationByTemplateCodeTenantCodeAndLovSignerType
						(dt.getDocTemplateCode(), bean.getSignerType(), tenant.getTenantCode());
				if (signLocs.isEmpty()) {
					continue;
				}
				
				MsDocTemplateSignLoc sl = signLocs.get(0);
				SignatureDetailBean psl = gson.fromJson(sl.getPrivySignLocation(), SignatureDetailBean.class);
				recepient.setPosX(String.valueOf(psl.getX()).replace(".0", ""));
				recepient.setPosY(String.valueOf(psl.getY()).replace(".0", ""));
				recepient.setSignPage(String.valueOf(sl.getSignPage()));
			} else  {
				recepient.setPosX(bean.getSignLocations().get(0).getUrx());
				recepient.setPosY(bean.getSignLocations().get(0).getUry());
				recepient.setSignPage(String.valueOf(bean.getSignLocations().get(0).getPage()));
			}
			
			
			recepients.add(recepient);
		}
		
		request.setRecipients(recepients);
	}
	
	private void prepareUploadDocManualSign(PrivyGeneralUploadDocumentRequest request, TrDocumentD docD, InsertDocumentManualSignRequest manual) {
		request.setReferenceNumber(docD.getDocumentId().replaceAll(CONST_REFNUMBER_REPLACE, ""));
		
		PrivyGeneralUploadDocumentDocumentBean document = new PrivyGeneralUploadDocumentDocumentBean();
		document.setBarcodePosition("0");
		document.setDocumentFile(CONST_DOCUMENT_FILE_PDFBASE64 + manual.getDocumentFile());
		document.setDocumentName(manual.getDocumentName());
		document.setNotifyUser("0");
		document.setSignProcess("1".equals(manual.getIsSequence()) ? "0" : "1");
		
		request.setDocument(document);
		
		List<PrivyGeneralUploadRecipientBean> recepients = new ArrayList<>();
		for (ManualSignerBean bean : manual.getSigners()) {
			PrivyGeneralUploadRecipientBean recepient = new PrivyGeneralUploadRecipientBean();
			recepient.setDetail("1");
			recepient.setNotifyUser("0");
			recepient.setDragNDrop(false);
			recepient.setSignerType(CONST_SIGNERTYPE_SIGNER);
			recepient.setAutosign(0);
			recepient.setEnterpriseToken("");
				
			MsVendorRegisteredUser vru = daoFactory.getVendorRegisteredUserDao().getVendorRegisteredUserByPhoneAndVendorCode(bean.getPhone(), GlobalVal.VENDOR_CODE_PRIVY_ID);
			recepient.setIdUser(vru.getVendorRegistrationId());
			recepient.setUserType("0");
			
			List<PrivyGeneralSignPositionBean> signPositions = new ArrayList<>();
			for (MsDocTemplateSignLoc sl : signLocs) {
				SignatureDetailBean psl = gson.fromJson(sl.getPrivySignLocation(), SignatureDetailBean.class);
				PrivyGeneralSignPositionBean position = new PrivyGeneralSignPositionBean(
					String.valueOf(psl.getX()).replace(".0", ""),
					String.valueOf(psl.getY()).replace(".0", ""),
					String.valueOf(sl.getSignPage())
				);
				signPositions.add(position);
			}
			recepient.setSignPositions(signPositions);
			
			recepients.add(recepient);
		}
		
		request.setRecipients(recepients);
	}
	
	@Override
	public PrivyGeneralOtpRequestSigningResponse otpRequestSigning(String signerUserId, MsTenant tenant, MsVendor vendor, List<String> documentId, 
			AuditContext audit) {
		
		MsVendoroftenant vendoroftenant = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
		
		try {
			// Prepare json request
			PrivyGeneralOtpRequestSigningRequest otpRequest = new PrivyGeneralOtpRequestSigningRequest();
			otpRequest.setReferenceNumbers(documentId);
			otpRequest.setChannelId(vendoroftenant.getVendorChannelId());
			otpRequest.setSignerUserId(signerUserId);
			String jsonRequest = gson.toJson(otpRequest);
			LOG.info("OTP Request Signing General Privy request: {}", jsonRequest);
			
			// Headers
			Headers headers = buildPrivyApiHeader(jsonRequest, vendoroftenant, audit);
			
			// Body
			RequestBody body = RequestBody.create(MediaType.parse(HttpHeaders.APPLICATION_JSON), jsonRequest);
			
			Request okHttpRequest = new Request.Builder()
					.headers(headers)
					.url(baseUrl + otpRequestUrl)
					.post(body)
					.build();
			
			OkHttpClient client = new OkHttpClient.Builder()
					.connectTimeout(10L, TimeUnit.SECONDS)
					.readTimeout(60L, TimeUnit.SECONDS)
					.build();
			
			Date startTime = new Date();
			Response okHttpResponse = client.newCall(okHttpRequest).execute();
			Date finishTime = new Date();
			logProcessDuration("OTP Request Signing General Privy", startTime, finishTime);
			
			String jsonResponse = okHttpResponse.body().string();
			LOG.info("OTP Request Signing General Privy response code: {}, body: {}", okHttpResponse.code(), jsonResponse);
			return gson.fromJson(jsonResponse, PrivyGeneralOtpRequestSigningResponse.class);
			
		} catch (Exception e) {
			
			LOG.error("OTP Request Signing General Privy exception: {}", e.getLocalizedMessage(), e);
			PrivyGeneralOtpRequestSigningResponse response = new PrivyGeneralOtpRequestSigningResponse();
			response.setMessage(e.getLocalizedMessage());
			return response;
			
		}
	}

	@Override
	public String buildOtpRequestSigningErrorMessage(PrivyGeneralOtpRequestSigningResponse response,
			AuditContext audit) {
		
		if (StringUtils.isNotBlank(response.getMessage())) {
			return response.getMessage();
		}
		
		if (null == response.getError() || CollectionUtils.isEmpty(response.getError().getErrors())) {
			return StringUtils.EMPTY;
		}
		
		if (response.getError().getErrors().get(0) instanceof String) {
			return response.getError().getErrors().get(0);
		}
		
		return "OTP Request gagal";
	}

	@Override
	public PrivyGeneralOtpValidationResponse otpValidation(String otpCode, TrPsreSigningConfirmation psreSigningConfirmation, MsTenant tenant,
			MsVendor vendor, AuditContext audit) {

		try {
			MsVendoroftenant vendoroftenant = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
			PrivyGeneralOtpValidationRequest request = new PrivyGeneralOtpValidationRequest();
			request.setOtpCode(otpCode);
			request.setTransactionId(psreSigningConfirmation.getTransactionId());
			String jsonRequest = gson.toJson(request);
			LOG.info("Otp Validation Request: {}", jsonRequest);

			// Headers
			Headers headers = buildPrivyApiHeader(jsonRequest, vendoroftenant, audit);

			// Body
			RequestBody body = RequestBody.create(MediaType.parse(HttpHeaders.APPLICATION_JSON), jsonRequest);

			Request okHttpRequest = new Request.Builder().headers(headers).url(baseUrl + otpValidationUrl).post(body)
					.build();
			
			OkHttpClient client = new OkHttpClient.Builder().connectTimeout(10L, TimeUnit.SECONDS)
					.readTimeout(60L, TimeUnit.SECONDS).build();

			Date startTime = new Date();
			Response okHttpResponse = client.newCall(okHttpRequest).execute();
			Date finishTime = new Date();
			logProcessDuration("OTP Validation Privy", startTime, finishTime);

			String jsonResponse = okHttpResponse.body().string();
			LOG.info("OTP Validation Privy response code: {}, body: {}", okHttpResponse.code(), jsonResponse);
			
			return gson.fromJson(jsonResponse, PrivyGeneralOtpValidationResponse.class);

		} catch (Exception e) {
			LOG.error("OTP Validation Privy exception: {}", e.getLocalizedMessage(), e);
			PrivyGeneralOtpValidationResponse response = new PrivyGeneralOtpValidationResponse();
			response.setMessage(e.getLocalizedMessage());
			return response;
		}
	}
	
	@Override
	public PrivyGeneralOtpValidationResponse otpValidation(String otpCode, TrPsreSigningConfirmation psreSigningConfirmation, MsTenant tenant,
			MsVendor vendor, SigningProcessAuditTrailBean auditTrailBean, AuditContext audit) {

		try {
			MsVendoroftenant vendoroftenant = daoFactory.getVendorDao().getVendoroftenant(tenant, vendor);
			PrivyGeneralOtpValidationRequest request = new PrivyGeneralOtpValidationRequest();
			request.setOtpCode(otpCode);
			request.setTransactionId(psreSigningConfirmation.getTransactionId());
			String jsonRequest = gson.toJson(request);
			LOG.info("Otp Validation Request: {}", jsonRequest);

			// Headers
			Headers headers = buildPrivyApiHeader(jsonRequest, vendoroftenant, audit);

			// Body
			RequestBody body = RequestBody.create(MediaType.parse(HttpHeaders.APPLICATION_JSON), jsonRequest);

			Request okHttpRequest = new Request.Builder().headers(headers).url(baseUrl + otpValidationUrl).post(body)
					.build();
			
			OkHttpClient client = new OkHttpClient.Builder().connectTimeout(10L, TimeUnit.SECONDS)
					.readTimeout(60L, TimeUnit.SECONDS).build();

			Date startTime = new Date();
			Response okHttpResponse = client.newCall(okHttpRequest).execute();
			Date finishTime = new Date();
			logProcessDuration("OTP Validation Privy", startTime, finishTime);

			String jsonResponse = okHttpResponse.body().string();
			LOG.info("OTP Validation Privy response code: {}, body: {}", okHttpResponse.code(), jsonResponse);
			
			PrivyGeneralOtpValidationResponse otpValidationResult = gson.fromJson(jsonResponse, PrivyGeneralOtpValidationResponse.class);
			
			String resultStatus = "0";
			
			if (otpValidationResult.getError() == null) {
				resultStatus = "1";
			}
			
			saveSignPrivyAuditTrail(auditTrailBean, resultStatus, jsonResponse, jsonRequest, audit);
			
			return otpValidationResult;

		} catch (Exception e) {
			LOG.error("OTP Validation Privy exception: {}", e.getLocalizedMessage(), e);
			PrivyGeneralOtpValidationResponse response = new PrivyGeneralOtpValidationResponse();
			
			PrivyGeneralErrorBean errorBean = new PrivyGeneralErrorBean();
			errorBean.setCode(StatusCode.PRIVY_ERROR);
			
			response.setMessage(e.getLocalizedMessage());
			response.setError(errorBean);
			return response;
		}
	}
	
	public void saveSignPrivyAuditTrail(SigningProcessAuditTrailBean auditTrailBean, String resultStatus, String response, String request, AuditContext audit) {
		
		MsLov lovProcessType = daoFactory.getLovDao().getMsLovByGroupAndCode(GlobalVal.LOV_GROUP_SIGNING_PROCESS_TYPE, GlobalVal.CODE_LOV_SIGNING_REQUESTED);
		
		TrSigningProcessAuditTrail auditTrail = new TrSigningProcessAuditTrail();
		
		auditTrail.setPhoneNoBytea(personalDataEncLogic.encryptFromString(auditTrailBean.getPhone()));
		auditTrail.setHashedPhoneNo(MssTool.getHashedString(auditTrailBean.getPhone()));
		auditTrail.setEmail(auditTrailBean.getEmail());
		auditTrail.setAmMsUser(auditTrailBean.getUser());
		auditTrail.setMsTenant(auditTrailBean.getTenant());
		auditTrail.setMsVendor(auditTrailBean.getVendorPsre());
		auditTrail.setLovProcessType(lovProcessType);
		auditTrail.setResultStatus(resultStatus);
		auditTrail.setDtmCrt(new Date());
		auditTrail.setUsrCrt(auditTrailBean.getEmail());
		
		daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailNewTr(auditTrail);
		
		if (resultStatus.equals("1")) {
			auditTrailLogic.logProcessRequestResponse(auditTrail, GlobalVal.AUDIT_TRAIL_SIGNING_PROCESS, request, response, false, audit);	
			String notes = String.format(GlobalVal.AUDIT_TRAIL_PRIVY_SIGN_LOG, auditTrail.getIdSigningProcessAuditTrail());
			auditTrail.setNotes(notes);
			daoFactory.getSigningProcessAuditTrailDao().updateSigningProcessAuditTrail(auditTrail);
		} 
		
		
		for (TrDocumentD docD : auditTrailBean.getDocumentDs()) {
			TrSigningProcessAuditTrailDetail auditTrailDetail = new TrSigningProcessAuditTrailDetail();
			auditTrailDetail.setDtmCrt(new Date());
			auditTrailDetail.setUsrCrt(auditTrailBean.getEmail());
			auditTrailDetail.setSigningProcessAuditTrail(auditTrail);
			auditTrailDetail.setTrDocumentD(docD);
			daoFactory.getSigningProcessAuditTrailDao().insertSigningProcessAuditTrailDetailNewTr(auditTrailDetail);;
		}
	}

	@Override
	public String buildOtpValidationErrorMessage(PrivyGeneralOtpValidationResponse response, AuditContext audit) {
		if (StringUtils.isNotBlank(response.getMessage())) {
			return response.getMessage();
		}

		if (null == response.getError() || CollectionUtils.isEmpty(response.getError().getErrors())) {
			return StringUtils.EMPTY;
		}

		if (response.getError().getErrors().get(0) instanceof String) {
			return (String) response.getError().getErrors().get(0);
		}

		return "OTP Validation gagal";
	}
}
